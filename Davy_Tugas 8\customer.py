pelanggan_hari_1 = {"<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"}
pelanggan_hari_2 = {"<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"}
pelanggan_hari_3 = {"<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"}

pelanggan_hari_1_saja = pelanggan_hari_1 - pelanggan_hari_2 - pelanggan_hari_3
print("Pelanggan hari pertama saja:", pelanggan_hari_1_saja)

pelanggan_semua_hari = pelanggan_hari_1 & pelanggan_hari_2 & pelanggan_hari_3
print("Pelanggan yang datang pada semua hari:", pelanggan_semua_hari)

pelanggan_dua_hari = (pelanggan_hari_1 & pelanggan_hari_2) | \
                     (pelanggan_hari_2 & pelanggan_hari_3) | \
                     (pelanggan_hari_1 & pelang<PERSON>_hari_3)
print("Pelanggan yang datang minimal dua hari:", pelanggan_dua_hari)

pelanggan_hari_2.add("Irma")
print("Pelanggan hari kedua setelah ditambah:", pelanggan_hari_2)

pelanggan_unik = pelanggan_hari_1 | pelanggan_hari_2 | pelanggan_hari_3
print("Total pelanggan unik selama tiga hari:", len(pelanggan_unik))