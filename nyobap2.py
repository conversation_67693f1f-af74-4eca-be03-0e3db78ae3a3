import pandas as pd

def hitung_min_uas(nilai_absen, nilai_tugas, nilai_uts, nilai_lulus=65):
    bobot_absen, bobot_tugas, bobot_uts, bobot_uas = 0.1, 0.2, 0.3, 0.4
    kontribusi = nilai_absen * bobot_absen + nilai_tugas * bobot_tugas + nilai_uts * bobot_uts
    min_uas = (nilai_lulus - kontribusi) / bobot_uas
    if min_uas > 100:
        return "Mustahil lulus"
    elif min_uas < 0:
        return "Sudah lulus"
    return round(min_uas, 2)

# Baca data dari Excel (ganti path sesuai file Anda)
df = pd.read_excel("PEMBAGIAN SESI.xlsx")

# Tambahkan kolom hasil perhitungan
df["Minimal UAS"] = df.apply(lambda row: hitung_min_uas(row["Absensi"], row["Tugas"], row["UTS"]), axis=1)

# Cetak hasil
print(df)
